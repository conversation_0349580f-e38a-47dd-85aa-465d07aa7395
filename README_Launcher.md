# 🔥 Fusion Launcher

Ein moderner Launcher für den Fusion Client mit rotem Design.

## Features

- **Automatische Minecraft-Erkennung**: Prüft ob Minecraft läuft
- **Smart Launch**: Startet Minecraft automatisch falls nötig
- **Download-Funktion**: Lädt Client-Updates aus dem Web
- **Modernes Design**: Rotes Farbschema mit CustomTkinter
- **Discord Integration**: <PERSON><PERSON><PERSON> Link zu deinem Discord Server

## Installation

1. <PERSON><PERSON> sicher, dass Python installiert ist
2. Installiere die benötigten Pakete:
```bash
pip install customtkinter psutil pillow requests
```

## Anpassungen

### URLs und Links ändern:
Bearbeite diese Zeilen in `FusionLauncher.py`:

```python
self.exe_url = "https://dl.dropboxusercontent.com/scl/fi/176p5t6fgsfm8m87g2elp/FusionClient.exe?rlkey=oxqavcolm03h1nr4tl8z3d40s&st=8oxrddsp"  # Dropbox Direct Link
self.discord_invite = "https://discord.gg/your-server"   # Dein Discord Server
```

### Version ändern:
```python
self.version = "1.21.82"  # Deine aktuelle Version
```

### Client-Datei ändern:
```python
self.exe_filename = "minecraft_cheat.py"  # Name der Client-Datei
```

## Funktionsweise

1. **Launch Button drücken**
2. **Launcher macht:**
   - Löscht alte Client-Datei (falls vorhanden)
   - Lädt IMMER die neueste Version herunter
   - Prüft ob Minecraft.Windows.exe läuft
3. **Wenn Minecraft nicht läuft:**
   - Startet Minecraft automatisch
   - Wartet bis Minecraft geladen ist
4. **Startet den frisch heruntergeladenen Fusion Client**

## Farben anpassen

Das rote Farbschema kann in der `colors` Dictionary geändert werden:

```python
self.colors = {
    "bg": "#1a0d0d",        # Hintergrund
    "frame_bg": "#2d1414",  # Frame-Hintergrund
    "accent": "#ff3333",    # Akzentfarbe
    "button_bg": "#cc2929", # Button-Hintergrund
    # ...
}
```

## Troubleshooting

- **"Minecraft startup timeout"**: Minecraft braucht länger zum Starten - erhöhe den Timeout
- **Download-Fehler**: Prüfe die `exe_url` und Internetverbindung
- **Discord-Button funktioniert nicht**: Prüfe den Discord-Invite Link in den Einstellungen

## Status-Meldungen

- `Ready to launch` - Bereit zum Starten
- `Downloading latest client...` - Lädt immer neueste Version herunter
- `Downloading... X.X%` - Download-Fortschritt
- `Download complete` - Download fertig
- `Minecraft not running, starting...` - Startet Minecraft
- `Launching Fusion Client...` - Startet Client
- `Client launched successfully` - Erfolgreich gestartet

## Wichtiger Hinweis

Der Launcher lädt **bei jedem Start** die neueste Version herunter. Es wird keine lokale Kopie wiederverwendet, um sicherzustellen, dass immer die aktuellste Version verwendet wird.
