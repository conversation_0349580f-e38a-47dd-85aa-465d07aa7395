import customtkinter as ctk
import os
import subprocess
import psutil
import threading
import time
import webbrowser
from PIL import Image
import requests
import base64
import io
import json
import shutil
from pathlib import Path
try:
    from pypresence import Presence
    DISCORD_RPC_AVAILABLE = True
except ImportError:
    DISCORD_RPC_AVAILABLE = False
    print("Discord RPC not available - install pypresence")

# Embedded Fusion icon as Base64 (split for readability)
FUSION_ICON_BASE64 = ("iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAACkElEQVR4nOWYvW4TQRSFvzk7a8eIR0hBkHgJpLwBEh1QIfEIKREhiNDQgGh4BISoCBSUFKShpaBFUFBDA7azf2iWDbJQing9u2vrfo"
                      "21subunDN7Z+4dt71zpcIwwjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4wjjCOL6LoDlQDSDEtRwXXfzlSkx6NCEFvriK31RLm+BjTmQK3CxT9vNxPak+DAhGHynnwM9ajfcxxd8oUw7zMQlQ0j0THEfK2PczTqB+7yAGTB"
                      "vxjxrxBf2s/Gtl3PUzspbio5wCw4gPK5+vLH5lA4YTv/rKr5wC0zPEtzmGlmGr3vDiiW9tQBB7q0x5mI/r57KHiipMNNZn/3/cpc/5nUrcy8dcAN67guf+pDagSxPCkfrZFVHFtzIgDPjqSh77OQ/yMbtVwqfS8ySZ1xPrMg3SyO"
                      "Jbp0BY6RfKKBM4LMbsFaM60LPGhE1CbQa5ZkN6mWQc+DkZFddLz0VcLwVQTHzbgf9MUIb3cKcYbWRr6VcZvGjCd8p6o+r6KIyNXzWAazanDyrq3/CctSyKRgNcUPhYgYL4QNgDrlYJ25WWMiGI/+gKvrmy143Uxw4Y6oTbRcq1Mm"
                      "V2job4bxo53ijnnc97TyHfRdCQAkH8/Jy1/avI5e0yiAGJ3dhslAGTDhqbjTFgErGfX8s94BR3hsOjNfjsOzdAjbBwZ7B4Uxtedqyc+2sivhMDXNMo7Zaet0nGU53U+X7KT1fV9cE6iO/EgFAQHatgz8+4VIkfruLXwv9d3xusRQ"
                      "pshYsS5fWFyeLqryPq0tnzFEJDoy6Db0JnKIwjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4wjjCOMI4yjoScwNH8AJEC7uVxsAXkAAAAASUVORK5CYII=")

class DiscordRPC:
    """Discord Rich Presence Manager for Fusion Launcher"""

    def __init__(self, client_id):
        self.client_id = client_id
        self.rpc = None
        self.connected = False
        self.enabled = False

    def connect(self):
        """Connect to Discord RPC"""
        if not DISCORD_RPC_AVAILABLE:
            return False

        try:
            self.rpc = Presence(self.client_id)
            self.rpc.connect()
            self.connected = True
            return True
        except Exception as e:
            print(f"Failed to connect to Discord RPC: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """Disconnect from Discord RPC"""
        if self.rpc and self.connected:
            try:
                self.rpc.close()
            except:
                pass
            self.connected = False

    def update_status(self, state="Idling in launcher", large_image="fusion_logo"):
        """Update Discord status"""
        if not self.enabled or not self.connected or not self.rpc:
            return

        try:
            self.rpc.update(
                state=state,
                large_image=large_image,
                large_text="Fusion Client Launcher"
            )
        except Exception as e:
            print(f"Failed to update Discord status: {e}")

    def set_enabled(self, enabled):
        """Enable or disable Discord RPC"""
        self.enabled = enabled
        if enabled and not self.connected:
            self.connect()
            if self.connected:
                self.update_status()
        elif not enabled and self.connected:
            self.disconnect()

class FusionLauncher:
    def __init__(self):
        # Load configuration first
        self.config = self.load_config()

        # CustomTkinter Setup
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("dark-blue")

        # Main Window
        self.root = ctk.CTk()
        self.root.title("Fusion Launcher")
        self.root.geometry("500x450")
        self.root.resizable(False, False)
        # Don't use overrideredirect to keep taskbar entry

        # Bring window to front
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))

        # Center window on screen
        self.center_window()

        # Red Color Theme - More rounded and modern
        self.colors = {
            "bg": "#1a0d0d",  # Dark red background
            "frame_bg": "#2d1414",  # Darker red for frames
            "accent": "#ff3333",  # Bright red accent
            "button_bg": "#cc2929",  # Red button background
            "button_hover": "#ff4444",  # Lighter red on hover
            "launch_bg": "#ff1a1a",  # Bright red for launch button
            "launch_hover": "#ff5555",  # Even brighter for hover
            "text": "#ffffff",  # White text
            "text_secondary": "#cccccc"  # Light gray text
        }

        # Configure window background
        self.root.configure(fg_color=self.colors["bg"])

        # Variables - CUSTOMIZE THESE
        self.version = "Loading..."  # Will be loaded from Dropbox
        self.version_url = "https://www.dropbox.com/scl/fi/sfwl8gneljrgc1qrlimnj/version.txt?rlkey=mo5f9u2uvl28q8qei7mpru907&st=ia2r2d8c&dl=1"
        self.exe_url = "https://dl.dropboxusercontent.com/scl/fi/176p5t6fgsfm8m87g2elp/FusionClient.exe?rlkey=oxqavcolm03h1nr4tl8z3d40s&st=8oxrddsp"
        self.minecraft_process_name = "Minecraft.Windows.exe"
        self.discord_invite = "https://discord.gg/5pRZnHjvJ4"  # Replace with your Discord server

        # Setup temp directory for client files
        self.setup_temp_directory()

        # Session download tracking
        self.client_downloaded_this_session = False

        # Client process monitoring
        self.client_running = False
        self.monitoring_client = False

        # Initialize Discord RPC
        self.discord_rpc = DiscordRPC(self.config["discord_rpc"]["client_id"])
        if self.config["discord_rpc"]["enabled"]:
            self.discord_rpc.set_enabled(True)
            # Set initial status after a short delay to ensure connection
            self.root.after(1000, lambda: self.discord_rpc.update_status("Idling in launcher"))

        # Load window icon
        self.load_window_icon()

        # Current page tracking
        self.current_page = "main"

        self.setup_ui()

        # Load version from Dropbox after UI is set up
        self.root.after(500, self.load_version_from_dropbox)

        # Center window and bring to front after everything is loaded
        self.root.after(100, self.center_window)
        self.root.after(200, self.bring_to_front)

    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def bring_to_front(self):
        """Bring window to front"""
        self.root.lift()
        self.root.focus_force()
        self.root.attributes('-topmost', True)
        self.root.after(100, lambda: self.root.attributes('-topmost', False))

    def load_window_icon(self):
        """Load window icon from embedded Base64 data"""
        try:
            import tkinter as tk

            # Decode Base64 icon data
            icon_data = base64.b64decode(FUSION_ICON_BASE64)
            icon_image = Image.open(io.BytesIO(icon_data))

            # Convert to RGBA and resize for different sizes
            icon_image = icon_image.convert("RGBA")

            # Create multiple sizes for better Windows integration
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64)]
            icons = []

            for size in sizes:
                resized = icon_image.resize(size, Image.Resampling.LANCZOS)
                # Convert to PhotoImage
                buffer = io.BytesIO()
                resized.save(buffer, format='PNG')
                buffer.seek(0)
                photo_data = base64.b64encode(buffer.getvalue()).decode()
                photo = tk.PhotoImage(data=photo_data)
                icons.append(photo)

            # Set the main icon (largest one)
            self.root.iconphoto(True, *icons)

            # Store references to prevent garbage collection
            self.icon_photos = icons

            # Also try to set the window icon using wm_iconbitmap for better taskbar support
            try:
                # Create a temporary .ico file for better Windows integration
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.ico', delete=False) as tmp_file:
                    # Convert PNG to ICO format
                    icon_image_ico = icon_image.resize((32, 32), Image.Resampling.LANCZOS)
                    icon_image_ico.save(tmp_file.name, format='ICO')
                    self.root.iconbitmap(tmp_file.name)
                    # Clean up temp file after a delay
                    self.root.after(1000, lambda: self.cleanup_temp_icon(tmp_file.name))
            except Exception as ico_error:
                print(f"Could not set ICO icon: {ico_error}")

        except Exception as e:
            print(f"Could not load embedded icon: {e}")
            # Fallback: try to load from Fusion.jpg if it exists
            try:
                if os.path.exists("Fusion.jpg"):
                    import tkinter as tk
                    icon_image = Image.open("Fusion.jpg")
                    icon_image = icon_image.convert("RGBA")
                    icon_image = icon_image.resize((32, 32), Image.Resampling.LANCZOS)

                    # Convert to PhotoImage format
                    buffer = io.BytesIO()
                    icon_image.save(buffer, format='PNG')
                    buffer.seek(0)
                    photo_data = base64.b64encode(buffer.getvalue()).decode()

                    photo = tk.PhotoImage(data=photo_data)
                    self.root.iconphoto(True, photo)
                    self.icon_photo = photo
                else:
                    print("No Fusion.jpg fallback found")
            except Exception as e2:
                print(f"Fallback icon method also failed: {e2}")

    def cleanup_temp_icon(self, filepath):
        """Clean up temporary icon file"""
        try:
            os.unlink(filepath)
        except:
            pass

    def setup_temp_directory(self):
        """Setup temporary directory for client files in AppData\\Local\\FusionClient\\Temp"""
        # Get AppData\Local\FusionClient\Temp path
        appdata_local = Path(os.environ.get('LOCALAPPDATA', os.path.expanduser('~/.local/share')))
        self.temp_dir = appdata_local / "FusionClient" / "Temp"

        # Clean up any existing temp directory first
        if self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
            except Exception as e:
                print(f"Could not clean existing temp directory: {e}")

        # Create fresh temp directory
        try:
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            print(f"Created temp directory: {self.temp_dir}")
        except Exception as e:
            print(f"Could not create temp directory: {e}")
            # Fallback to current directory if temp creation fails
            self.temp_dir = Path(".")

        # Set the full path for the exe file
        self.exe_filepath = self.temp_dir / "FusionClient.exe"

    def cleanup_temp_directory(self):
        """Clean up the temporary directory"""
        if hasattr(self, 'temp_dir') and self.temp_dir.exists() and self.temp_dir.name == "Temp":
            try:
                shutil.rmtree(self.temp_dir)
                print(f"Cleaned up temp directory: {self.temp_dir}")
            except Exception as e:
                print(f"Could not clean up temp directory: {e}")

    def load_version_from_dropbox(self):
        """Load version from Dropbox version.txt file"""
        def download_version():
            try:
                response = requests.get(self.version_url, timeout=10)
                response.raise_for_status()

                # Get version from response and clean it
                version = response.text.strip()

                # Update version in main thread
                self.root.after(0, lambda: self.update_version_display(version))

            except Exception as e:
                print(f"Failed to load version from Dropbox: {e}")
                # Fallback to default version
                self.root.after(0, lambda: self.update_version_display("1.21.82"))

        # Run in separate thread to avoid blocking UI
        threading.Thread(target=download_version, daemon=True).start()

    def update_version_display(self, version):
        """Update the version display in the UI"""
        self.version = version
        # Update the version label if it exists
        if hasattr(self, 'version_value_label'):
            self.version_value_label.configure(text=self.version)



    def setup_ui(self):
        # Main container with padding and very rounded corners
        self.main_container = ctk.CTkFrame(
            self.root,
            fg_color=self.colors["bg"],
            corner_radius=35  # More rounded
        )
        self.main_container.pack(fill="both", expand=True, padx=15, pady=15)

        # Header with title and back button
        self.header_frame = ctk.CTkFrame(
            self.main_container,
            fg_color=self.colors["frame_bg"],
            corner_radius=30,  # More rounded
            height=70
        )
        self.header_frame.pack(fill="x", pady=(0, 15))
        self.header_frame.pack_propagate(False)

        # Back button (initially hidden)
        self.back_button = ctk.CTkButton(
            self.header_frame,
            text="← Back",
            width=80,
            height=35,
            command=self.show_main_page,
            corner_radius=20,
            font=("Segoe UI", 14, "bold"),
            fg_color=self.colors["button_bg"],
            hover_color=self.colors["button_hover"],
            text_color="white"
        )
        # Don't pack initially - will be shown when needed

        # Title (centered)
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="🔥 Fusion Launcher 🔥",
            font=("Segoe UI", 26, "bold"),
            text_color=self.colors["accent"]
        )
        self.title_label.pack(pady=20)

        # Create content container for page switching
        self.content_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        self.content_container.pack(fill="both", expand=True)

        # Create main page
        self.create_main_page()

        # Create settings page (initially hidden)
        self.create_settings_page()

        # Show main page initially
        self.show_main_page()

    def create_main_page(self):
        """Create the main launcher page"""
        self.main_page = ctk.CTkFrame(
            self.content_container,
            fg_color=self.colors["frame_bg"],
            corner_radius=30  # More rounded
        )

        # Version display - More rounded
        version_frame = ctk.CTkFrame(
            self.main_page,
            fg_color=self.colors["bg"],
            corner_radius=25,  # More rounded
            height=65
        )
        version_frame.pack(pady=25, padx=25, fill="x")
        version_frame.pack_propagate(False)

        version_label = ctk.CTkLabel(
            version_frame,
            text="Version:",
            font=("Segoe UI", 18),
            text_color=self.colors["text"]
        )
        version_label.pack(side="left", padx=25, pady=18)

        self.version_value_label = ctk.CTkLabel(
            version_frame,
            text=self.version,
            font=("Segoe UI", 18, "bold"),
            text_color=self.colors["accent"]
        )
        self.version_value_label.pack(side="right", padx=25, pady=18)

        # Launch button (main button) - ABOVE settings - More rounded
        self.launch_button = ctk.CTkButton(
            self.main_page,
            text="Launch",
            width=320,
            height=75,
            command=self.launch_client,
            corner_radius=25,  # More rounded
            font=("Segoe UI", 18, "bold"),  # Same as Settings button
            fg_color=self.colors["launch_bg"],
            hover_color=self.colors["launch_hover"],
            text_color="white"
        )
        self.launch_button.pack(pady=20)

        # Settings button - BELOW launch - More rounded
        settings_button = ctk.CTkButton(
            self.main_page,
            text="Settings",
            width=250,
            height=55,
            command=self.open_settings,
            corner_radius=25,  # More rounded
            font=("Segoe UI", 18, "bold"),
            fg_color=self.colors["button_bg"],
            hover_color=self.colors["button_hover"],
            text_color="white"
        )
        settings_button.pack(pady=15)

        # Status label
        self.status_label = ctk.CTkLabel(
            self.main_page,
            text="Ready to launch",
            font=("Segoe UI", 14),
            text_color=self.colors["text_secondary"]
        )
        self.status_label.pack(pady=(0, 25))

        # Discord button (bottom right) - Same styling as Settings button
        discord_button = ctk.CTkButton(
            self.main_page,
            text="Discord",
            width=80,
            height=35,
            command=self.open_discord,
            corner_radius=25,  # Same as Settings button
            font=("Segoe UI", 12, "bold"),
            fg_color=self.colors["button_bg"],  # Red theme
            hover_color=self.colors["button_hover"],
            text_color="white"
        )
        discord_button.place(relx=1.0, rely=1.0, anchor="se", x=-15, y=-15)

    def create_settings_page(self):
        """Create the settings page"""
        self.settings_page = ctk.CTkFrame(
            self.content_container,
            fg_color=self.colors["frame_bg"],
            corner_radius=30
        )

        # Discord RPC Section (direkt ohne extra Titel)
        discord_frame = ctk.CTkFrame(
            self.settings_page,
            fg_color=self.colors["bg"],
            corner_radius=25
        )
        discord_frame.pack(fill="x", padx=25, pady=(25, 15))

        discord_label = ctk.CTkLabel(
            discord_frame,
            text="Discord Rich Presence",
            font=("Segoe UI", 16, "bold"),
            text_color=self.colors["text"]
        )
        discord_label.pack(pady=(20, 10))

        # Discord RPC Toggle
        self.discord_rpc_var = ctk.BooleanVar(value=self.config["discord_rpc"]["enabled"])
        discord_switch = ctk.CTkSwitch(
            discord_frame,
            text="Enable Discord RPC",
            variable=self.discord_rpc_var,
            command=self.toggle_discord_rpc,
            font=("Segoe UI", 14),
            text_color=self.colors["text"],
            fg_color=self.colors["button_bg"],
            progress_color=self.colors["accent"]
        )
        discord_switch.pack(pady=(5, 20))

        # Save button
        save_button = ctk.CTkButton(
            self.settings_page,
            text="💾 Save Settings",
            command=self.save_settings_and_back,
            corner_radius=25,
            font=("Segoe UI", 16, "bold"),
            fg_color=self.colors["launch_bg"],
            hover_color=self.colors["launch_hover"],
            text_color="white",
            width=250,
            height=50
        )
        save_button.pack(pady=30)

    def show_main_page(self):
        """Show the main launcher page"""
        self.current_page = "main"
        self.settings_page.pack_forget()
        self.main_page.pack(fill="both", expand=True)

        # Update header
        self.title_label.configure(text="🔥 Fusion Launcher 🔥")
        self.back_button.pack_forget()

    def show_settings_page(self):
        """Show the settings page"""
        self.current_page = "settings"
        self.main_page.pack_forget()
        self.settings_page.pack(fill="both", expand=True)

        # Update header
        self.title_label.configure(text="⚙️ Settings")
        self.back_button.pack(side="left", padx=20, pady=17)

    def save_settings_and_back(self):
        """Save settings and go back to main page"""
        # Update config
        self.config["discord_rpc"]["enabled"] = self.discord_rpc_var.get()

        # Save to file
        self.save_config()

        # Go back to main page
        self.show_main_page()

    def get_config_path(self):
        """Get the path to the config directory"""
        # AppData\Local\FusionClient\Launcher\
        appdata_local = Path(os.environ.get('LOCALAPPDATA', os.path.expanduser('~/.local/share')))
        config_dir = appdata_local / "FusionClient" / "Launcher"

        # Create directories if they don't exist
        config_dir.mkdir(parents=True, exist_ok=True)

        return config_dir / "config.json"

    def load_config(self):
        """Load launcher configuration"""
        # Default config without sensitive data
        default_config = {
            "discord_rpc": {
                "enabled": True
            },
            "appearance": {
                "theme": "red"
            }
        }

        config_file = self.get_config_path()

        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                # Merge with defaults for missing keys
                for key in default_config:
                    if key not in config:
                        config[key] = default_config[key]
                    elif isinstance(default_config[key], dict):
                        for subkey in default_config[key]:
                            if subkey not in config[key]:
                                config[key][subkey] = default_config[key][subkey]

                # Add client_id programmatically (not saved to file)
                config["discord_rpc"]["client_id"] = "1377037066818293900"
                return config
        except (FileNotFoundError, json.JSONDecodeError):
            # Save default config if file doesn't exist
            self.save_default_config(config_file, default_config)
            # Add client_id programmatically
            default_config["discord_rpc"]["client_id"] = "1377037066818293900"
            return default_config

    def save_default_config(self, config_file, default_config):
        """Save default configuration to file"""
        try:
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=4)
        except Exception as e:
            print(f"Error saving default config: {e}")

    def save_config(self):
        """Save launcher configuration"""
        config_file = self.get_config_path()
        try:
            # Create a copy of config without sensitive data
            config_to_save = {
                "discord_rpc": {
                    "enabled": self.config["discord_rpc"]["enabled"]
                    # client_id is not saved - it's hardcoded
                },
                "appearance": self.config.get("appearance", {"theme": "red"})
            }

            with open(config_file, 'w') as f:
                json.dump(config_to_save, f, indent=4)
        except Exception as e:
            print(f"Error saving config: {e}")

    def minimize_window(self):
        # Minimize to taskbar
        self.root.iconify()

    def open_settings(self):
        """Open settings in the same window"""
        self.show_settings_page()

    def open_discord(self):
        # Open Discord server
        webbrowser.open(self.discord_invite)

    def toggle_discord_rpc(self):
        """Toggle Discord RPC on/off"""
        enabled = self.discord_rpc_var.get()
        self.discord_rpc.set_enabled(enabled)
        if enabled:
            self.discord_rpc.update_status("Idling in launcher")

    def is_minecraft_running(self):
        """Check if Minecraft.Windows.exe is running"""
        for proc in psutil.process_iter(['name']):
            try:
                if self.minecraft_process_name.lower() in proc.info['name'].lower():
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False

    def is_fusion_client_running(self):
        """Check if FusionClient.exe is running"""
        for proc in psutil.process_iter(['name']):
            try:
                if "fusionclient.exe" in proc.info['name'].lower():
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return False

    def monitor_client_process(self):
        """Monitor FusionClient process and update Discord status accordingly"""
        if not self.monitoring_client:
            return

        client_currently_running = self.is_fusion_client_running()

        # Client started
        if client_currently_running and not self.client_running:
            self.client_running = True
            self.discord_rpc.update_status("Playing FusionClient")

        # Client stopped
        elif not client_currently_running and self.client_running:
            self.client_running = False
            self.discord_rpc.update_status("Idling in launcher")

        # Schedule next check in 3 seconds
        self.root.after(3000, self.monitor_client_process)

    def download_client(self):
        """Download the client executable to temp directory - Fresh download every time"""
        try:
            # Always download fresh - no session caching
            self.status_label.configure(text="Downloading Latest Client...")
            self.discord_rpc.update_status("Downloading client")
            self.launch_button.configure(state="disabled")

            # Delete existing file first
            if self.exe_filepath.exists():
                try:
                    self.exe_filepath.unlink()
                except:
                    pass  # Ignore if file can't be deleted

            # SPEED OPTIMIZATIONS:
            # 1. Larger chunk size for faster downloads
            # 2. Custom headers to optimize connection
            # 3. Session with connection pooling
            # 4. Disable SSL verification for speed (if needed)

            session = requests.Session()

            # Optimize headers for faster download
            headers = {
                'User-Agent': 'FusionLauncher/1.0',
                'Accept': '*/*',
                'Accept-Encoding': 'identity',  # Disable compression to save CPU
                'Connection': 'keep-alive',
                'Cache-Control': 'no-cache'
            }

            # Start download with optimized settings
            response = session.get(
                self.exe_url,
                stream=True,
                headers=headers,
                timeout=(10, 30)  # (connect timeout, read timeout)
            )
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            last_progress_update = 0

            # Use larger chunk size for faster downloads (1MB chunks)
            chunk_size = 1024 * 1024  # 1MB chunks instead of 64KB

            with open(self.exe_filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            # Update UI less frequently to avoid slowing down
                            if progress - last_progress_update >= 10:  # Every 10% instead of every 1%
                                self.status_label.configure(text=f"Downloading... {progress:.0f}%")
                                self.discord_rpc.update_status(f"Downloading {progress:.0f}%")
                                last_progress_update = progress

            self.status_label.configure(text="Download complete")
            self.discord_rpc.update_status("Download complete")
            self.client_downloaded_this_session = True  # Mark as downloaded this session

            # Close session
            session.close()
            return True

        except Exception as e:
            self.status_label.configure(text=f"Download failed: {str(e)}")
            self.discord_rpc.update_status("Download failed")
            return False
        finally:
            self.launch_button.configure(state="normal")

    def launch_minecraft(self):
        """Launch Minecraft if not running"""
        try:
            self.status_label.configure(text="Starting Minecraft...")
            self.discord_rpc.update_status("Starting Minecraft")
            # Launch Minecraft (adjust path as needed)
            subprocess.Popen("start minecraft:", shell=True)

            # Wait for Minecraft to start
            timeout = 30  # 30 seconds timeout
            start_time = time.time()

            while time.time() - start_time < timeout:
                if self.is_minecraft_running():
                    self.status_label.configure(text="Minecraft started")
                    self.discord_rpc.update_status("Minecraft started")
                    return True
                time.sleep(1)

            self.status_label.configure(text="Minecraft startup timeout")
            self.discord_rpc.update_status("Minecraft timeout")
            return False

        except Exception as e:
            self.status_label.configure(text=f"Failed to start Minecraft: {str(e)}")
            self.discord_rpc.update_status("Minecraft failed")
            return False

    def launch_client_exe(self):
        """Launch the client executable from temp directory"""
        try:
            self.status_label.configure(text="Launching Fusion Client...")
            self.discord_rpc.update_status("Launching client")

            # Check if exe exists in temp directory
            if not self.exe_filepath.exists():
                self.status_label.configure(text="Client not found")
                self.discord_rpc.update_status("Client not found")
                return False

            # Launch the downloaded exe from temp directory
            subprocess.Popen([str(self.exe_filepath)], shell=True)

            self.status_label.configure(text="Client launched successfully")

            # Start monitoring the client process
            if not self.monitoring_client:
                self.monitoring_client = True
                self.client_running = True
                self.discord_rpc.update_status("Playing FusionClient")
                # Start monitoring after a short delay
                self.root.after(5000, self.monitor_client_process)

            return True

        except Exception as e:
            self.status_label.configure(text=f"Failed to launch client: {str(e)}")
            self.discord_rpc.update_status("Launch failed")
            return False

    def launch_client(self):
        """Main launch function"""
        def launch_thread():
            try:
                self.launch_button.configure(state="disabled")

                # Step 1: Download/check client
                if not self.download_client():
                    return

                # Step 2: Check if Minecraft is running
                if not self.is_minecraft_running():
                    self.status_label.configure(text="Minecraft not running, starting...")
                    if not self.launch_minecraft():
                        return

                # Step 3: Launch client
                time.sleep(2)  # Give Minecraft time to fully load
                self.launch_client_exe()

            finally:
                self.launch_button.configure(state="normal")

        # Run in separate thread to prevent UI freezing
        threading.Thread(target=launch_thread, daemon=True).start()

    def on_closing(self):
        """Handle window closing"""
        # Stop monitoring
        self.monitoring_client = False

        # Clean up temp directory
        self.cleanup_temp_directory()

        # Disconnect Discord RPC
        self.discord_rpc.disconnect()
        self.root.quit()
        self.root.destroy()

    def run(self):
        # Set close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    launcher = FusionLauncher()
    launcher.run()
